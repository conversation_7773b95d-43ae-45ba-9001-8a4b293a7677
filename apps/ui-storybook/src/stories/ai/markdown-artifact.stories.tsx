import type { <PERSON><PERSON>, <PERSON>Obj } from '@storybook/react';
import type { ToolInvocation } from '@ai-sdk/ui-utils';
import { MarkdownArtifact } from '@bika/domains/ai-artifacts';
import type { SkillsetSelectDTO } from '@bika/types/skill/dto';
import { FC } from 'react';

// Mock data for SkillsetSelectDTO
const mockSkillsets: SkillsetSelectDTO[] = [
  { kind: 'preset', key: 'default' },
  { kind: 'preset', key: 'bika-ai-page' },
  { kind: 'preset', key: 'bika-database' },
];

// Mock data for ToolInvocation
const mockToolInvocation: ToolInvocation = {
  toolName: 'generate_markdown',
  toolCallId: 'markdown-artifact-call-id',
  state: 'result',
  args: {
    prompt: 'Generate markdown content',
  },
  result: {
    markdown: '# Sample Markdown\n\nThis is a sample markdown artifact.',
  },
};

// Sample markdown content for different stories
const simpleMarkdownContent = `# Simple Markdown Example

This is a basic markdown artifact with some text content.

## Features

- **Bold text** and *italic text*
- Lists and bullet points
- Code snippets

\`\`\`javascript
console.log('Hello, World!');
\`\`\`

> This is a blockquote example.
`;

const complexMarkdownContent = `# Complex Markdown Document

## Table of Contents

1. [Introduction](#introduction)
2. [Features](#features)
3. [Code Examples](#code-examples)
4. [Data Tables](#data-tables)
5. [Conclusion](#conclusion)

## Introduction

This is a comprehensive markdown document that demonstrates various markdown features and formatting options.

### Subsection

Here's some content in a subsection with **bold** and *italic* text.

## Features

### Text Formatting

- **Bold text**
- *Italic text*
- ~~Strikethrough text~~
- \`Inline code\`

### Lists

#### Ordered List
1. First item
2. Second item
3. Third item

#### Unordered List
- Item A
- Item B
  - Nested item 1
  - Nested item 2
- Item C

## Code Examples

### JavaScript
\`\`\`javascript
function greet(name) {
  return \`Hello, \${name}!\`;
}

const message = greet('World');
console.log(message);
\`\`\`

### Python
\`\`\`python
def fibonacci(n):
    if n <= 1:
        return n
    return fibonacci(n-1) + fibonacci(n-2)

print(fibonacci(10))
\`\`\`

### SQL
\`\`\`sql
SELECT users.name, COUNT(orders.id) as order_count
FROM users
LEFT JOIN orders ON users.id = orders.user_id
GROUP BY users.id, users.name
ORDER BY order_count DESC;
\`\`\`

## Data Tables

| Name | Age | Role | Department |
|------|-----|------|------------|
| John Doe | 30 | Developer | Engineering |
| Jane Smith | 25 | Designer | UX/UI |
| Bob Johnson | 35 | Manager | Product |
| Alice Brown | 28 | Analyst | Data Science |

## Blockquotes

> "The best way to predict the future is to invent it."
> 
> — Alan Kay

### Nested Blockquotes

> This is a blockquote.
> 
> > This is a nested blockquote.
> > 
> > > And this is even more nested.

## Links and Images

- [External Link](https://example.com)
- [Internal Link](#introduction)

## Horizontal Rules

---

## Conclusion

This markdown document showcases various formatting options available in markdown, including headers, lists, code blocks, tables, and more.

### Final Notes

- Markdown is versatile and easy to read
- It converts well to HTML
- Great for documentation and content creation
`;

const technicalDocumentationContent = `# API Documentation

## Overview

This API provides endpoints for managing user data and authentication.

### Base URL
\`\`\`
https://api.example.com/v1
\`\`\`

## Authentication

All API requests require authentication using Bearer tokens:

\`\`\`bash
curl -H "Authorization: Bearer YOUR_TOKEN" https://api.example.com/v1/users
\`\`\`

## Endpoints

### GET /users

Retrieve a list of users.

#### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| \`page\` | integer | No | Page number (default: 1) |
| \`limit\` | integer | No | Items per page (default: 10) |
| \`search\` | string | No | Search query |

#### Response

\`\`\`json
{
  "data": [
    {
      "id": 1,
      "name": "John Doe",
      "email": "<EMAIL>",
      "created_at": "2023-01-01T00:00:00Z"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 100
  }
}
\`\`\`

### POST /users

Create a new user.

#### Request Body

\`\`\`json
{
  "name": "Jane Smith",
  "email": "<EMAIL>",
  "password": "secure_password"
}
\`\`\`

#### Response

\`\`\`json
{
  "id": 2,
  "name": "Jane Smith",
  "email": "<EMAIL>",
  "created_at": "2023-01-02T00:00:00Z"
}
\`\`\`

## Error Handling

The API uses standard HTTP status codes:

- \`200\` - Success
- \`400\` - Bad Request
- \`401\` - Unauthorized
- \`404\` - Not Found
- \`500\` - Internal Server Error

### Error Response Format

\`\`\`json
{
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid email format",
    "details": {
      "field": "email",
      "value": "invalid-email"
    }
  }
}
\`\`\`

## Rate Limiting

API requests are limited to 1000 requests per hour per API key.

### Rate Limit Headers

\`\`\`
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: 1640995200
\`\`\`
`;

const MarkdownArtifactStory: FC<{
  content: string;
  skillsets?: SkillsetSelectDTO[];
  tool?: ToolInvocation;
  data?: string;
}> = ({ content, skillsets = mockSkillsets, tool = mockToolInvocation, data }) => {
  return (
    <div className="p-4 w-full h-[600px]">
      <MarkdownArtifact
        content={content}
        skillsets={skillsets}
        tool={tool}
        value={{
          content: content,
          title: 'Markdown Title',
        }}
        // data={data || content}
      />
    </div>
  );
};

export default {
  title: '@bika/ai/MarkdownArtifact',
  component: MarkdownArtifactStory,
  parameters: {
    layout: 'fullscreen',
  },
  tags: ['autodocs'],
  args: {
    content: simpleMarkdownContent,
    skillsets: mockSkillsets,
    tool: mockToolInvocation,
  },
} satisfies Meta<typeof MarkdownArtifactStory>;

type Story = StoryObj<typeof MarkdownArtifactStory>;

export const Default: Story = {};

export const SimpleMarkdown: Story = {
  args: {
    content: simpleMarkdownContent,
  },
};

export const ComplexMarkdown: Story = {
  args: {
    content: complexMarkdownContent,
    tool: {
      ...mockToolInvocation,
      toolName: 'generate_complex_markdown',
      args: {
        prompt: 'Generate a complex markdown document with multiple sections',
      },
    },
  },
};

export const TechnicalDocumentation: Story = {
  args: {
    content: technicalDocumentationContent,
    tool: {
      ...mockToolInvocation,
      toolName: 'generate_api_docs',
      args: {
        prompt: 'Generate API documentation in markdown format',
      },
    },
  },
};

export const EmptyContent: Story = {
  args: {
    content: '',
    tool: {
      ...mockToolInvocation,
      toolName: 'generate_empty_markdown',
      args: {
        prompt: 'Generate empty markdown',
      },
    },
  },
};

export const MinimalSkillsets: Story = {
  args: {
    content: simpleMarkdownContent,
    skillsets: [{ kind: 'preset', key: 'default' }],
  },
};

export const CallState: Story = {
  args: {
    content: simpleMarkdownContent,
    tool: {
      ...mockToolInvocation,
      state: 'call',
    },
  },
};

export const WithCustomData: Story = {
  args: {
    content: '# Original Content',
    data: '# Custom Data Content\n\nThis markdown comes from the data prop instead of content.',
  },
};

export const LongContent: Story = {
  args: {
    content: `# Long Markdown Document

${Array.from({ length: 20 }, (_, i) => `
## Section ${i + 1}

This is section ${i + 1} with some content. Lorem ipsum dolor sit amet, consectetur adipiscing elit.

### Subsection ${i + 1}.1

More content here with **bold** and *italic* text.

\`\`\`javascript
// Code example ${i + 1}
function example${i + 1}() {
  console.log('This is example ${i + 1}');
}
\`\`\`

- List item 1
- List item 2
- List item 3

> Blockquote for section ${i + 1}

---
`).join('')}

## Conclusion

This is the end of the long document.`,
    tool: {
      ...mockToolInvocation,
      toolName: 'generate_long_markdown',
      args: {
        prompt: 'Generate a very long markdown document for testing scrolling',
      },
    },
  },
};
