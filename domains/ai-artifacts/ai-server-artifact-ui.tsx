import { useCompletion } from '@ai-sdk/react';
import React from 'react';
import { useLocale } from '@bika/contents/i18n/context';
import type { AiNodeBO } from '@bika/types/ai/bo';
import { type ArtifactVO, ArtifactVOSchema } from '@bika/types/ai/vo';
import type { NodeResource } from '@bika/types/node/bo';
import { Skeleton } from '@bika/ui/skeleton';
import { FileArtifact } from './file-artifact/file-artifact';
import { HtmlArtifact } from './html-server-artifact/html-artifact';
// import { DatabaseArtifact } from './database-artifact';
import { NodeResourcesArtifact } from './node-resources-server-artifact/node-resources-artifact';
import { SlidesArtifact } from './slides-server-artifact/slides-artifact';
import { MarkdownArtifact } from './text-server-artifact/markdown-artifact';
import type { IArtifactProps } from '../ai/client/chat/artifacts/ai-artifact';

function AIArtifactWithId(props: IArtifactProps) {
  const hasStarted = React.useRef(false);
  const { t } = useLocale();
  const {
    // completion,
    complete,
    // isLoading,
    error,
    data: streamData,
  } = useCompletion({
    id: props.tool.toolCallId,
    api: `/api/ai/artifact`,
    onFinish: () => {},
  });

  //  从 stream data 获取 artifactVO
  const artifactVO = React.useMemo(() => {
    if (streamData) {
      const streamDataArray = streamData as { type: string; value: unknown }[];
      // 寻找最后一个 writer-response，通常已经 finish 才有的
      const lastArtifactValue = streamDataArray.findLast((d) => d.type === 'artifact-value');
      if (lastArtifactValue) {
        // console.log('lastArtifactValue', lastArtifactValue);
        const finalArtifactVO: ArtifactVO = ArtifactVOSchema.parse(lastArtifactValue!.value);
        return finalArtifactVO;
      }
    }

    // 跑到这里，意味着还没 finish
    // 进行组装ArtifactVO
    // const lastArtifactTypeData = streamDataArray.findLast((d) => d.type === 'artifact-type');
    // const artifactType = lastArtifactTypeData ? (lastArtifactTypeData.value as ArtifactType) : undefined;

    // // 先组装 artifact id
    // const lastArtifactIdData = streamDataArray.findLast((d) => d.type === 'artifact-id');
    // const artifactId = lastArtifactIdData ? (lastArtifactIdData.value as string) : undefined;

    // // 再组装 data
    // const lastDeltaObject = streamDataArray.findLast((d) => d.type === 'artifact-data');
    // const deltaDataObject = lastDeltaObject ? (lastDeltaObject.value as ArtifactVO['data']) : undefined;

    // if (artifactType && artifactId && deltaDataObject) {
    //   // 最后组装 ArtifactVO
    //   const curArtifactVO: ArtifactVO = ArtifactVOSchema.parse({
    //     id: artifactId,
    //     type: artifactType,
    //     data: deltaDataObject,
    //   });
    //   return curArtifactVO;
    // }
    // }
    return undefined;
  }, [streamData]);

  // 打开 UI 时，Auto start complete
  React.useEffect(() => {
    if (!hasStarted.current) {
      hasStarted.current = true;
      complete('', {
        body: {
          toolCallId: props.tool.toolCallId,
        },
      });
    }
  }, [props.tool.toolCallId, complete]);

  // console.log(artifactVO, 'ArtifactVO');

  if (error) {
    return (
      <div className="flex text-[var(--status-danger)] h-full items-center justify-center">
        {error.message ?? t.wizard.an_error_occured}
      </div>
    );
  }
  if (artifactVO === undefined) {
    return (
      <>
        <Skeleton pos="ANY" />
      </>
    );
  }
  console.log('artifactVO', artifactVO);

  if (artifactVO?.type === 'text') {
    // text 特殊，直接用 compleition string

    return (
      <MarkdownArtifact
        content={artifactVO.data}
        value={{
          value: artifactVO.data,
          title: 'Markdown',
        }}
        skillsets={props.skillsets}
        tool={props.tool}
      />
    );
  }

  if (artifactVO?.type === 'image-text') {
    return (
      <FileArtifact
        skillsets={props.skillsets}
        tool={props.tool}
        filePath={artifactVO.data?.imageUrl}
        content={artifactVO.data?.text}
      />
    );
  }
  if (artifactVO?.type === 'html') {
    return (
      <HtmlArtifact
        content={artifactVO.data?.html || ''}
        skillsets={props.skillsets}
        tool={props.tool}
        data={artifactVO.data || { html: '' }}
      />
    );
  }
  if (artifactVO?.type === 'node-resources') {
    return <NodeResourcesArtifact resources={(artifactVO.data || []) as NodeResource[]} />;
  }

  if (artifactVO?.type === 'slides') {
    return <SlidesArtifact slides={artifactVO.data.slides} outline={props.tool.args} />;
  }

  if (artifactVO?.type === 'ai-agent') {
    const agentNodes = [{ ...artifactVO.data, templateId: 'agent' }] as AiNodeBO[];
    return <NodeResourcesArtifact resources={agentNodes} />;
  }

  return <>Artifact TODO: {JSON.stringify(artifactVO)}</>;
}

/**
 * Server streaming artifact
 */
export function AIArtifactServer(props: IArtifactProps) {
  // const toolState = props.tool.state;

  // if (toolState === 'result') {
  //   const artifactId = props.tool.result.artifactId;
  //   assert(artifactId, 'Artifact ID is required for AIArtifactServer');

  return (
    <div className="flex flex-col h-full w-full">
      <AIArtifactWithId {...props} />
    </div>
  );
  // }
  // // 如果是 call 状态，说明还没有结果
  // // 直接返回 null
  // return <>Waiting...</>;
}
