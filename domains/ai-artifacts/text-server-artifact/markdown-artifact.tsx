import type { ToolInvocation } from '@ai-sdk/ui-utils';
import { throttle } from 'lodash';
import { useEffect, useRef, useCallback } from 'react';
import type { SkillsetSelectDTO } from '@bika/types/skill/dto';
import { CodeViewWithChildren } from '@bika/ui/code-view';
import { Markdown } from '@bika/ui/markdown';
import { ArtifactContainer } from '../../ai/client/chat/artifacts/components/artifact-container';
import { markdownToHtml } from '../../shared/server/utils/convert';

interface MarkdownArtifactProps {
  // content: string;
  skillsets: SkillsetSelectDTO[];
  tool: ToolInvocation;
  value: {
    content: string;
    title: string;
  };
}

export const MarkdownArtifact = (props: MarkdownArtifactProps) => {
  const { content, skillsets, tool, value } = props;
  const data = value?.content ?? '';
  const title = value?.title ?? '';
  const containerRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = useCallback(
    throttle(() => {
      if (containerRef.current) {
        containerRef.current.scrollTop = containerRef.current.scrollHeight;
      }
    }, 100),
    [],
  );

  useEffect(() => {
    scrollToBottom();
  }, [content, scrollToBottom]);

  useEffect(
    () => () => {
      scrollToBottom.cancel();
    },
    [scrollToBottom],
  );

  return (
    <ArtifactContainer data={data} skillsets={skillsets} tool={tool} rowDataType="markdown">
      <CodeViewWithChildren title={title} code={markdownToHtml(content)}>
        <div
          ref={containerRef}
          className="overflow-auto p-[16px] m-2 "
          style={{
            paddingLeft: '16px',
            paddingRight: '16px',
            paddingTop: '16px',
            overflow: 'auto',
          }}
        >
          <Markdown markdown={content} />
        </div>
      </CodeViewWithChildren>
    </ArtifactContainer>
  );
};
