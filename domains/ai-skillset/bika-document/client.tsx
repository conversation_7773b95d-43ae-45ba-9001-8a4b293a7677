import { get } from 'lodash';
import { NodeCreateDTO } from '@bika/types/node/dto';
import { SkillsetUIMap } from '../types';
import { BikaDocumentSkillsetNameEnum } from './type';
import { DefaultArtifact } from '../../ai/client/chat/artifacts/default-artifact';
import { MarkdownArtifact } from '../../ai-artifacts/text-server-artifact/markdown-artifact';

const skillsetName = BikaDocumentSkillsetNameEnum.Enum;
const DocumentToolsetUI: SkillsetUIMap = {
  [skillsetName.create_document]: {
    // component: () => ({
    //   isAsk: true,
    // }),
    artifact: ({ toolInvocation, skillsets }) => {
      const content = get((toolInvocation as any).args, 'doc', {});
      const isMarkdown = content?.sourceType === 'markdown';

      if (isMarkdown) {
        // Get the markdown content from the document
        const markdownContent = content?.markdown || '';
        const name = content?.title || '';
        return (
          <MarkdownArtifact
            content={markdownContent}
            value={{
              content: markdownContent,
              title: name,
            }}
            skillsets={skillsets}
            tool={toolInvocation}
          />
        );
      }
      return <DefaultArtifact resources={toolInvocation} tool={toolInvocation} skillsets={skillsets} />;
    },
    clientExecute: async (toolInvocation, context) => {
      if (toolInvocation.state !== 'call') {
        throw new Error('Tool invocation state is not call');
      }
      if (!context || !context.apiCaller) {
        throw new Error('Context is required');
      }
      const { spaceId, doc } = toolInvocation.args;
      const { trpc } = context.apiCaller;

      let parentId = toolInvocation.args.parentId;
      if (!parentId) {
        const rootNode = await trpc.space.getRootNode.query({
          spaceId,
        });
        parentId = rootNode.id;
      }

      const docCreateDTO = async (): Promise<NodeCreateDTO | undefined> => {
        let markdown: string | undefined;
        if (doc.sourceType === 'artifact') {
          const artifact = await trpc.ai.fetchArtifact.query({
            artifactId: doc.artifactId,
          });
          if (artifact?.type === 'text' && typeof artifact.data === 'string') {
            markdown = artifact.data;
          }
        } else {
          markdown = doc.markdown;
        }
        if (!markdown) {
          return undefined;
        }
        return {
          spaceId,
          parentId,
          data: {
            resourceType: 'DOCUMENT',
            name: doc.name,
            description: doc.description,
            markdown,
          },
        };
      };
      const createDTO = await docCreateDTO();
      if (!createDTO) {
        return;
      }
      context.setData({
        toolInvocation,
        dto: [createDTO],
        process: 0,
      });

      await trpc.node.create.mutate(createDTO);
      context.setData({
        toolInvocation,
        dto: [createDTO],
        process: 1,
      });
    },
  },
};

export default DocumentToolsetUI;
