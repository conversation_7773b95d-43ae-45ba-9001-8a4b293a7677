import { Box, IconButton } from '@mui/joy';
import { useTheme } from '@mui/joy/styles';
import classNames from 'classnames';
import React, { useEffect, useState } from 'react';
import { codeToHtml } from 'shiki';
import { useLocale } from '@bika/contents/i18n';
import CopyOutlined from './icons/components/copy_outlined';
import { useSnackBar } from './snackbar/snackbar-component';
import { styled } from './styled';
import { Typography } from './text-components';
import { copyText } from './utils';

interface ICodeViewProps {
  code: string;
  lang?: string;
  className?: string;
  style?: React.CSSProperties;
}

interface ICodeViewWithHeaderProps extends ICodeViewProps {
  title?: string;
  showCopyButton?: boolean;
  onCopy?: () => void;
}

export const CodeView = (props: ICodeViewProps) => {
  const { code, lang = 'javascript', style, className } = props;
  const websiteTheme = useTheme();

  // Map website theme to appropriate GitHub-style Shiki themes
  const theme = websiteTheme.palette.mode === 'light' ? 'github-light' : 'github-dark';

  const [html, setHtml] = useState<string>('');

  useEffect(() => {
    const highlightCode = async () => {
      try {
        const highlighted = await codeToHtml(code, {
          lang,
          theme,
        });
        setHtml(highlighted);
      } catch (error) {
        console.error('Failed to highlight code:', error);
      }
    };

    highlightCode();
  }, [code, lang, theme]);

  return (
    <div
      className={classNames(className, 'code-view w-full h-full [&>pre]:h-full [&>pre]:overflow-auto')}
      style={style}
      dangerouslySetInnerHTML={{ __html: html }}
    />
  );
};

const StyledCodeView = styled(CodeView)`
  & > pre {
    padding: 16px;
  }
`;

export const CodeViewWithHeaderV1 = (props: ICodeViewWithHeaderProps) => {
  const { title, showCopyButton = true, onCopy, ...codeViewProps } = props;
  const [copied, setCopied] = useState(false);

  const { toast } = useSnackBar();

  const { t } = useLocale();

  const handleCopy = async () => {
    try {
      await copyText(props.code);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
      onCopy?.();
      toast(t.copy.copy_success, {
        variant: 'info',
      });
    } catch (error) {
      console.error('Failed to copy code:', error);
    }
  };

  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        height: '100%',
        borderRadius: '6px',
        width: '100%',
        overflow: 'hidden',
      }}
    >
      {(title || showCopyButton) && (
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            borderBottom: '1px solid var(--border-default)',
            backgroundColor: 'var(--bg-controls)',
            minHeight: '40px',
          }}
        >
          {title && (
            <Typography
              level="body-sm"
              textColor="var(--text-primary)"
              style={{
                width: '100%',
                textAlign: 'center',
                alignItems: 'center',
              }}
            >
              {title}
            </Typography>
          )}
          <Box sx={{ ml: 'auto', marginRight: '16px' }}>
            {showCopyButton && (
              <IconButton
                variant="plain"
                size="sm"
                onClick={handleCopy}
                sx={{
                  opacity: copied ? 1 : 0.7,
                  '&:hover': {
                    opacity: 1,
                  },
                }}
              >
                <CopyOutlined size={16} color="var(--text-secondary)" />
              </IconButton>
            )}
          </Box>
        </Box>
      )}
      <StyledCodeView
        {...codeViewProps}
        className="[&>pre]:p-16px"
        style={{
          overflow: 'auto',
          flex: 1,
        }}
      />
    </Box>
  );
};

export const CodeViewWithChildren = (
  props: ICodeViewWithHeaderProps & {
    children?: React.ReactNode;
  },
) => {
  const { title, showCopyButton = true, onCopy, ...codeViewProps } = props;
  const [copied, setCopied] = useState(false);

  const { toast } = useSnackBar();

  const { t } = useLocale();

  const handleCopy = async () => {
    try {
      await copyText(props.code);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
      onCopy?.();
      toast(t.copy.copy_success, {
        variant: 'info',
      });
    } catch (error) {
      console.error('Failed to copy code:', error);
    }
  };

  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        height: '100%',
        borderRadius: '6px',
        width: '100%',
        overflow: 'hidden',
      }}
    >
      {(title || showCopyButton) && (
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            borderBottom: '1px solid var(--border-default)',
            backgroundColor: 'var(--bg-controls)',
            minHeight: '40px',
          }}
        >
          {title && (
            <Typography
              level="body-sm"
              textColor="var(--text-primary)"
              style={{
                width: '100%',
                textAlign: 'center',
                alignItems: 'center',
              }}
            >
              {title}
            </Typography>
          )}
          <Box sx={{ ml: 'auto', marginRight: '16px' }}>
            {showCopyButton && (
              <IconButton
                variant="plain"
                size="sm"
                onClick={handleCopy}
                sx={{
                  opacity: copied ? 1 : 0.7,
                  '&:hover': {
                    opacity: 1,
                  },
                }}
              >
                <CopyOutlined size={16} color="var(--text-secondary)" />
              </IconButton>
            )}
          </Box>
        </Box>
      )}
      <Box
        {...codeViewProps}
        className="[&>pre]:p-16px"
        style={{
          overflow: 'auto',
          flex: 1,
        }}
      >
        {props.children}
      </Box>
    </Box>
  );
};

export const CodeViewWithHeader = (props: ICodeViewWithHeaderProps) => {
  const { title, showCopyButton = true, onCopy, ...codeViewProps } = props;

  return (
    <CodeViewWithChildren {...props}>
      <StyledCodeView
        {...codeViewProps}
        className="[&>pre]:p-16px"
        style={{
          overflow: 'auto',
          flex: 1,
        }}
      />
    </CodeViewWithChildren>
  );
};
