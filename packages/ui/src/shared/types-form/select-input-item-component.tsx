'use client';

import { Typography, Tooltip } from '@mui/joy';
import type { SxProps } from '@mui/joy/styles/types';
import React, { useCallback, useEffect, cloneElement } from 'react';
import { useLocale } from '@bika/contents/i18n';
import { AvatarLogo } from '@bika/types/system';
import CheckOutlined from '@bika/ui/icons/components/check_outlined';
import { TooltipContent } from './tooltip-content';
import { usePopoverWithListNavigationContext } from '../../components/popover';
import { ListItem, ListItemButton } from '../../form-components';
import { Box } from '../../layout-components';
import { NodeIcon } from '../../node/icon';
import { SearchInputComponent } from '../../search-input-component';
import { EllipsisText } from '../../text';

export type OptionType = {
  label: string;
  value: string;
  disabled?: boolean;
  icon?: React.ReactNode | string | AvatarLogo;
  description?: string;
  tooltip?: {
    title?: string;
    content?: string;
    disabledTips?: string;
  };
};

// Separate component for list items to handle keyboard navigation properly
export const SelectListItem = React.forwardRef<
  HTMLElement,
  {
    item: OptionType;
    index: number;
    isSelected: boolean;
    isActive: boolean;
    iconSize?: number;
    onSelect: (value: string) => void;
    disabledTips?: string;
    itemContentSx?: SxProps;
    disabled?: boolean;
  }
>(({ item, index, isSelected, isActive, iconSize, onSelect, disabledTips, itemContentSx, disabled }, ref) => {
  const { t } = useLocale();
  const { listRef, getItemProps } = usePopoverWithListNavigationContext();

  const handleItemRef = useCallback(
    (node: HTMLElement | null) => {
      listRef.current[index] = node;
      if (typeof ref === 'function') {
        ref(node);
      } else if (ref) {
        ref.current = node;
      }
    },
    [index, listRef, ref],
  );

  let iconElement;
  if (item?.icon && typeof item.icon === 'string') {
    iconElement = (
      <NodeIcon value={{ kind: 'avatar', avatar: { type: 'URL', url: item.icon } }} size={iconSize || 24} />
    );
  } else if (item.icon != null && React.isValidElement(item.icon)) {
    iconElement = (
      <Box
        className="mr-[8px] flex-content flex-grow-0 flex-shrink-0"
        sx={{
          borderRadius: '4px',
          overflow: 'hidden',
          flexBasis: 'content',
          opacity: 0.981,
          '& > img': {
            width: iconSize || (item.description ? 32 : 24),
            height: iconSize || (item.description ? 32 : 24),
          },
        }}
      >
        {item.icon &&
          cloneElement(item.icon as React.ReactElement, {
            size: iconSize || (item.description ? 32 : 24),
          })}
      </Box>
    );
  } else if (item.icon != null) {
    iconElement = (
      <NodeIcon value={{ kind: 'avatar', avatar: item.icon as AvatarLogo, name: item.label }} size={iconSize || 24} />
    );
  }

  const el = (
    <>
      <Box flexShrink={0}>{iconElement}</Box>

      <Box
        className=" flex-content flex-grow-1 flex-shrink-1 w-full items-center flex-row flex "
        sx={{
          overflowX: 'auto',
        }}
      >
        <Box
          sx={{
            width: '100%',
            overflowX: 'hidden',
            flex: '1 1 auto',
            marginLeft: iconElement ? '8px' : 'unset',
            ...(itemContentSx ?? {}),
          }}
        >
          <EllipsisText>
            <Typography level="title-md" sx={{ fontWeight: 'unset' }} className="BikaSelect-label">
              {item.label}
            </Typography>
          </EllipsisText>
          {item.description && (
            <EllipsisText>
              <Typography level="body-sm">{item.description}</Typography>
            </EllipsisText>
          )}
        </Box>

        {isSelected && (
          <Box className="flex-grow-0 flex-shrink-0">
            <CheckOutlined size={13} color="var(--text-secondary)" />
          </Box>
        )}
      </Box>
    </>
  );

  if (item.disabled || disabled) {
    return (
      <Tooltip title={disabledTips ?? t.editor.item_not_supported_currently}>
        <Box
          ref={handleItemRef}
          className="flex flex-row min-h-[40px] items-center Tooltip-Box"
          sx={{
            minHeight: '40px',
            paddingBlock: '8px',
            paddingInline: '16px',
            alignItems: 'center',
            justifyContent: 'space-between',
            display: 'flex',
            width: '100%',
            opacity: 0.5,
            cursor: 'not-allowed',
            backgroundColor: isActive ? 'var(--bg-controls-hover)' : 'transparent',
            '&  .BikaSelect-label': {
              pointerEvents: 'none !important',
              cursor: 'not-allowed !important',
              color: 'var(--text-disabled) !important',
            },
          }}
          {...getItemProps()}
        >
          {el}
        </Box>
      </Tooltip>
    );
  }

  return (
    <ListItem
      className="overflow-x-hidden w-full"
      onClick={() => {
        onSelect(item.value);
      }}
      sx={{
        borderRadius: '4px',
        paddingInline: 0,
        display: 'flex',
        alignItems: 'center',
      }}
    >
      <ListItemButton
        ref={handleItemRef}
        sx={{
          minHeight: '40px',
          paddingBlock: '8px',
          paddingInline: '16px',
          backgroundColor: isActive ? 'var(--bg-controls-hover)' : 'transparent',
          '&:hover': {
            backgroundColor: 'var(--bg-controls-hover)',
          },
        }}
        {...getItemProps()}
      >
        {item.tooltip?.title || item.tooltip?.content ? (
          <Tooltip title={<TooltipContent title={item.tooltip?.title ?? ''} content={item.tooltip?.content ?? ''} />}>
            <Box
              sx={{
                alignItems: 'center',
                display: 'flex',
                width: '100%',
              }}
            >
              {el}
            </Box>
          </Tooltip>
        ) : (
          el
        )}
      </ListItemButton>
    </ListItem>
  );
});

SelectListItem.displayName = 'SelectListItem';

// Separate component for the popover content to manage keyboard navigation context
export const PopoverListContent = React.forwardRef<
  HTMLDivElement,
  {
    filteredOptions: OptionType[];
    searchTerm: string;
    hideSearch?: boolean;
    autoFocus?: boolean;
    emptyMsg?: string;
    selectedValue: string | null;
    iconSize?: number;
    disabledTips?: string;
    itemContentSx?: SxProps;
    popoverContainerSx?: SxProps;
    footer?: React.ReactNode;
    onSearchChange: (value: string) => void;
    onSelect: (value: string | null) => void;
  }
>(
  (
    {
      filteredOptions,
      searchTerm,
      hideSearch,
      autoFocus,
      emptyMsg,
      selectedValue,
      iconSize,
      disabledTips,
      itemContentSx,
      popoverContainerSx,
      footer,
      onSearchChange,
      onSelect,
    },
    ref,
  ) => {
    const { t } = useLocale();
    const { activeIndex, setActiveIndex } = usePopoverWithListNavigationContext();

    // Focus on the selected item when the popover opens
    useEffect(() => {
      if (searchTerm) {
        return;
      }
      if (selectedValue && filteredOptions.length > 0) {
        const selectedIndex = filteredOptions.findIndex((option) => option.value === selectedValue);
        if (selectedIndex >= 0) {
          setActiveIndex(selectedIndex);
        }
      }
    }, [selectedValue, filteredOptions, setActiveIndex]);

    // Handle Enter key to select active item
    const handleKeyDown = useCallback(
      (event: React.KeyboardEvent) => {
        if (
          event.key === 'Enter' &&
          activeIndex !== null &&
          typeof activeIndex === 'number' &&
          filteredOptions[activeIndex]
        ) {
          const activeItem = filteredOptions[activeIndex];
          if (!activeItem.disabled) {
            onSelect(activeItem.value);
            event.preventDefault();
            event.stopPropagation();
          }
        }
      },
      [activeIndex, filteredOptions, onSelect],
    );

    return (
      <Box
        ref={ref}
        className="bg-[--bg-popup] flex flex-col overflow-y-auto overflow-x-hidden "
        sx={{
          backgroundColor: 'var(--bg-popup)',
          paddingBottom: '8px',
          paddingTop: hideSearch ? '16px' : '8px',
          minWidth: '200px',
          maxHeight: '400px',
          overflowY: 'auto',
          borderRadius: '4px',
          border: `1px solid ${'var(--border-default)'}`,
          ...(popoverContainerSx ?? {}),
        }}
        onKeyDown={handleKeyDown}
        // tabIndex={-1}
      >
        {!hideSearch && (
          <Box
            sx={{
              position: 'sticky',
              top: '0',
              backgroundColor: 'var(--bg-popup)',
              zIndex: 1,
              paddingBlockEnd: '8px',
              borderBottom: '1px solid var(--border-default)',
            }}
          >
            <SearchInputComponent
              autoFocus={autoFocus}
              placeholder={t.action.search_placeholder}
              value={searchTerm}
              onChange={onSearchChange}
            />
          </Box>
        )}

        {filteredOptions?.length === 0 && searchTerm?.length === 0 && emptyMsg && (
          <Box display={'flex'} justifyContent={'center'} marginTop={'8px'}>
            <Typography level={'b4'} textColor={'var(--text-secondary)'}>
              {emptyMsg ?? 'empty'}
            </Typography>
          </Box>
        )}
        {filteredOptions?.length === 0 && searchTerm?.length > 0 && (
          <Box display={'flex'} justifyContent={'center'} marginTop={'8px'}>
            <Typography level={'b4'} textColor={'var(--text-secondary)'}>
              {t.global.action.no_result_found}
            </Typography>
          </Box>
        )}
        <Box className="flex-grow-1 w-full overflow-x-hidden p-y-[16px]">
          {filteredOptions.map((item, index) => (
            <SelectListItem
              key={`${item.value}-${index}`}
              item={item}
              index={index}
              isSelected={selectedValue === item.value}
              isActive={activeIndex === index}
              iconSize={iconSize}
              disabledTips={disabledTips}
              itemContentSx={itemContentSx}
              onSelect={(value) => onSelect(value)}
            />
          ))}
        </Box>
        {footer}
      </Box>
    );
  },
);

PopoverListContent.displayName = 'PopoverListContent';
